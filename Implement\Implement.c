/************************************************************
 * ��Ȩ���Ͻ���ҵ��;������ѧϰʹ�á� 
 * �ļ���Implement.c
 * ����: ���Ǿ�
 * ƽ̨: ��Ծ����ҹ���߿�����
 * ΢��: YunXiang_TechShare  
 * Q Q: 2228398717
 * ���ںţ����Ǿ�
************************************************************/

/************************* ͷ�ļ� *************************/

#include "Implement.h"

/************************* �궨�� *************************/

/************************ �������� ************************/

uint16_t time_cnt=0;
uint32_t status_print_timer=0;

/************************ �������� ************************/

/************************************************************ 
 * ����:       show_address(u8 mode)
 * ˵��:       ��LCD����ʾ��ַ��Ϣ 
 * ����:       1 ��ʾDHCP��ȡ���ĵ�ַ
  	           ���� ��ʾ��̬��ַ
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void show_address(u8 mode)
{
	printf("\r\n=== Network Configuration ===\r\n");
	if(mode==1)
	{
		printf("DHCP Mode: Enabled\r\n");
		printf("MAC    :%02X.%02X.%02X.%02X.%02X.%02X\r\n",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//��ӡMAC��ַ
		printf("DHCP IP:%d.%d.%d.%d\r\n",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//��ӡ��̬IP��ַ
		printf("DHCP GW:%d.%d.%d.%d\r\n",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//��ӡ���ص�ַ
		printf("DHCP MASK:%d.%d.%d.%d\r\n",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//��ӡ���������ַ
	}
	else
	{
		printf("DHCP Mode: Disabled (Static IP)\r\n");
		printf("MAC      :%02X.%02X.%02X.%02X.%02X.%02X\r\n",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//��ӡMAC��ַ
		printf("Static IP:%d.%d.%d.%d\r\n",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//��ӡ��̬IP��ַ
		printf("Static GW:%d.%d.%d.%d\r\n",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//��ӡ���ص�ַ
		printf("Static MASK:%d.%d.%d.%d\r\n",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//��ӡ���������ַ
	}
	printf("=============================\r\n");
	printf("Network ready! You can now ping this device.\r\n\r\n");
}

/************************************************************ 
 * ����:       System_Init(void)
 * ˵��:       ϵͳ��ʼ��
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void System_Init(void)
{
    systick_config();     // ʱ������
	
	  nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);//����NVIC�жϷ���2:2λ��ռ���ȼ���2λ��Ӧ���ȼ�
	
	  USART0_Config();  // ���ڳ�ʼ��
	
	  LED_Init();  			//LED��ʼ��
	
	  KEY_Init();  			//������ʼ��

//	  LCD_Init(); 			//LCD��ʼ��(�Ѱ���)
	  
	  my_mem_init(SRAMIN);		//��ʼ���ڲ��ڴ��
	  my_mem_init(SRAMCCM);	//��ʼ��CCM�ڴ��
	
		// POINT_COLOR = RED; 		// LCD��ɫ���ã��Ѱ���
		printf("\r\n");
		printf("=====================================\r\n");
		printf("    LYIT GD32F4 Development Board   \r\n");
		printf("      Ethernet lwIP Test Project    \r\n");
		printf("         LYIT@GD32F470ZGT6          \r\n");
		printf("           Version: 2024/03/11      \r\n");
		printf("=====================================\r\n");
		printf("System Initializing...\r\n");
		Timer3_Init(9999,239); //100hz��Ƶ��

		printf("Initializing LWIP stack...\r\n");
		u8 lwip_init_result;
		while((lwip_init_result = lwip_comm_init()) != 0) //lwip��ʼ��
		{
			switch(lwip_init_result)
			{
				case 1:
					printf("LWIP Init Failed! Error: Memory allocation failed\r\n");
					break;
				case 2:
					printf("LWIP Init Failed! Error: LAN8720 PHY initialization failed\r\n");
					printf("Check: 1.Network cable connection 2.PHY power 3.Clock configuration\r\n");
					break;
				case 3:
					printf("LWIP Init Failed! Error: Network interface creation failed\r\n");
					break;
				default:
					printf("LWIP Init Failed! Error: Unknown error (%d)\r\n", lwip_init_result);
					break;
			}
			printf("Retrying in 3 seconds...\r\n");
			delay_1ms(3000);
		}
		printf("LWIP stack initialized successfully!\r\n");
    
		#if LWIP_DHCP   //ʹ��DHCP
			printf("Starting DHCP client...\r\n");
			while((lwipdev.dhcpstatus!=2)&&(lwipdev.dhcpstatus!=0XFF))//�ȴ�DHCP��ȡ�ɹ�/��ʱ���
			{
				lwip_periodic_handle();	//LWIP�ں���Ҫ��ʱ�����ĺ���
			}
			if(lwipdev.dhcpstatus==2)
			{
				printf("DHCP client started successfully!\r\n");
			}
			else
			{
				printf("DHCP failed, using static IP configuration.\r\n");
			}
		#endif
		  show_address(lwipdev.dhcpstatus);	//��ʾ��ַ��Ϣ

}

/************************************************************ 
 * ����:       Implement(void)
 * ˵��:       ִ�к���
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void Implement(void)
{
	 printf("Entering main loop - Network monitoring active...\r\n");
	 while(1)
	 {
        lwip_periodic_handle();	//LWIP�ں���Ҫ��ʱ�����ĺ���

        // ÿ10��ӡ��һ������״̬
        if(++status_print_timer >= 1000000) // ���ڴ�ѭ��Ƶ�ʵ���
        {
            status_print_timer = 0;
            printf("Network Status: IP=%d.%d.%d.%d, DHCP=%s\r\n",
                   lwipdev.ip[0], lwipdev.ip[1], lwipdev.ip[2], lwipdev.ip[3],
                   (lwipdev.dhcpstatus==2) ? "Active" : "Inactive");
        }
	 }
}


/****************************End*****************************/

