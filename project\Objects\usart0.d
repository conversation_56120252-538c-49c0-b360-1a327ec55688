.\objects\usart0.o: ..\Protocol\USART0\USART0.c
.\objects\usart0.o: ..\Protocol\USART0\USART0.h
.\objects\usart0.o: ..\HeaderFiles\HeaderFiles.h
.\objects\usart0.o: ..\CMSIS\gd32f4xx.h
.\objects\usart0.o: ..\CMSIS\core_cm4.h
.\objects\usart0.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\usart0.o: ..\CMSIS\core_cmInstr.h
.\objects\usart0.o: ..\CMSIS\core_cmFunc.h
.\objects\usart0.o: ..\CMSIS\core_cm4_simd.h
.\objects\usart0.o: ..\CMSIS\system_gd32f4xx.h
.\objects\usart0.o: ..\User\gd32f4xx_libopt.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\usart0.o: ..\CMSIS\gd32f4xx.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\usart0.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\usart0.o: ..\User\systick.h
.\objects\usart0.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\usart0.o: ..\Implement\Implement.h
.\objects\usart0.o: ..\HeaderFiles\HeaderFiles.h
.\objects\usart0.o: ..\HardWare\LED\LED.h
.\objects\usart0.o: ..\HardWare\KEY\KEY.h
.\objects\usart0.o: ..\System\TIMER\TIMER.h
.\objects\usart0.o: ..\LWIP\lwip_app\lwip_comm\lwip_comm.h
.\objects\usart0.o: ..\HardWare\LAN8720\lan8720.h
.\objects\usart0.o: ..\Protocol\USART0\USART0.h
.\objects\usart0.o: ..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h
.\objects\usart0.o: ..\LWIP\lwip-1.4.1\src\include\lwip/err.h
.\objects\usart0.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\usart0.o: ..\LWIP\lwip_app\lwip_comm\lwipopts.h
.\objects\usart0.o: ..\LWIP\lwip-1.4.1\src\include\lwip/debug.h
.\objects\usart0.o: ..\LWIP\lwip-1.4.1\src\include\lwip/arch.h
.\objects\usart0.o: ..\LWIP\arch/cc.h
.\objects\usart0.o: ..\LWIP\arch/cpu.h
.\objects\usart0.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\usart0.o: ..\LWIP\lwip-1.4.1\src\include\lwip/netif.h
.\objects\usart0.o: ..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h
.\objects\usart0.o: ..\LWIP\lwip-1.4.1\src\include\lwip/def.h
.\objects\usart0.o: ..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h
.\objects\usart0.o: ..\User\bsp.h
.\objects\usart0.o: ..\HardWare\SRAM\SRAM.h
.\objects\usart0.o: ..\MALLOC\malloc.h
.\objects\usart0.o: ..\HardWare\LCD\LCD.h
